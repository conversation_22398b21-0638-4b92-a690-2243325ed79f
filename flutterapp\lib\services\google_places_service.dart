import 'dart:convert';

import 'package:active_ecommerce_cms_demo_app/other_config.dart';
import 'package:http/http.dart' as http;

class GoogleReview {
  final String authorName;
  final double rating;
  final String text;
  final int time;
  final String? profilePhotoUrl;

  GoogleReview({
    required this.authorName,
    required this.rating,
    required this.text,
    required this.time,
    this.profilePhotoUrl,
  });
}

class PlaceDetails {
  final double rating;
  final int userRatingsTotal;
  final String url;
  final List<GoogleReview> reviews;
  const PlaceDetails({
    required this.rating,
    required this.userRatingsTotal,
    required this.url,
    required this.reviews,
  });
}

class GooglePlacesService {
  static const _detailsUrl =
      'https://maps.googleapis.com/maps/api/place/details/json';
  static const _findUrl =
      'https://maps.googleapis.com/maps/api/place/findplacefromtext/json';

  static Future<PlaceDetails?> fetchPlaceDetails({
    String? placeId,
    String? address,
  }) async {
    final key = OtherConfig.GOOGLE_PLACES_API_KEY;

    // Resolve placeId if not provided, using the address text query
    String? resolvedPlaceId = placeId;
    if ((resolvedPlaceId == null || resolvedPlaceId.isEmpty) &&
        address != null &&
        address.isNotEmpty) {
      final findUri = Uri.parse(_findUrl).replace(
        queryParameters: {
          'input': address,
          'inputtype': 'textquery',
          'fields': 'place_id',
          'key': key,
        },
      );
      final findResp = await http.get(findUri);
      if (findResp.statusCode == 200) {
        final findData = jsonDecode(findResp.body);
        final candidates = (findData['candidates'] as List? ?? []);
        if (candidates.isNotEmpty) {
          resolvedPlaceId = candidates.first['place_id'];
        }
      }
    }

    if (resolvedPlaceId == null || resolvedPlaceId.isEmpty) {
      return null;
    }

    final detailsUri = Uri.parse(_detailsUrl).replace(
      queryParameters: {
        'place_id': resolvedPlaceId,
        'fields': 'rating,reviews,user_ratings_total,url',
        'key': key,
        'reviews_sort': 'newest',
        'language': 'en',
      },
    );

    final resp = await http.get(detailsUri);
    if (resp.statusCode != 200) return null;
    final data = jsonDecode(resp.body);
    if (data['status'] != 'OK') return null;

    final result = data['result'];
    final reviews =
        (result['reviews'] as List? ?? [])
            .map(
              (r) => GoogleReview(
                authorName: r['author_name'] ?? 'Anonymous',
                rating:
                    (r['rating'] is int)
                        ? (r['rating'] as int).toDouble()
                        : (r['rating'] ?? 0.0).toDouble(),
                text: r['text'] ?? '',
                time: r['time'] ?? 0,
                profilePhotoUrl: r['profile_photo_url'],
              ),
            )
            .toList();

    return PlaceDetails(
      rating:
          (result['rating'] is int)
              ? (result['rating'] as int).toDouble()
              : (result['rating'] ?? 0.0).toDouble(),
      userRatingsTotal: result['user_ratings_total'] ?? 0,
      url: result['url'] ?? '',
      reviews: reviews,
    );
  }

  // Backward compatibility if anything else was using it
  static Future<List<GoogleReview>> fetchReviews({
    String? placeId,
    String? address,
  }) async {
    final details = await fetchPlaceDetails(placeId: placeId, address: address);
    return details?.reviews ?? [];
  }
}
