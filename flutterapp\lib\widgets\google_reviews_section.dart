
import 'package:active_ecommerce_cms_demo_app/my_theme.dart';
import 'package:active_ecommerce_cms_demo_app/other_config.dart';
import 'package:active_ecommerce_cms_demo_app/services/google_places_service.dart';
import 'package:flutter/material.dart';

class GoogleReviewsSection extends StatefulWidget {
  final String? title;
  const GoogleReviewsSection({super.key, this.title});

  @override
  State<GoogleReviewsSection> createState() => _GoogleReviewsSectionState();
}

class _GoogleReviewsSectionState extends State<GoogleReviewsSection> {
  late Future<PlaceDetails?> _future;

  @override
  void initState() {
    super.initState();
    // Try placeId first if present; otherwise resolve by address string
    final placeId = OtherConfig.GOOGLE_PLACE_ID;
    final address = OtherConfig.GOOGLE_PLACE_TEXT_QUERY.trim();
    _future = GooglePlacesService.fetchPlaceDetails(
      placeId: placeId.isEmpty ? null : placeId,
      address: (placeId.isEmpty && address.isNotEmpty) ? address : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    if ((OtherConfig.GOOGLE_PLACE_ID).isEmpty) {
      return const SizedBox.shrink();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Text(
            widget.title ?? 'What our customers say',
            style: TextStyle(
              color: MyTheme.accent_color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        FutureBuilder<PlaceDetails?>(
          future: _future,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
              );
            }
            if (snapshot.hasError) {
              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Unable to load reviews',
                  style: TextStyle(color: MyTheme.font_grey),
                ),
              );
            }
            final details = snapshot.data;
            final reviews = details?.reviews ?? [];
            if (reviews.isEmpty) {
              return const SizedBox.shrink();
            }
            // Header with rating and total count
            final header = Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Icon(Icons.star, color: Colors.amber[700], size: 18),
                  const SizedBox(width: 6),
                  Text(
                    '${details!.rating.toStringAsFixed(1)} (${details.userRatingsTotal})',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      // Open place URL if present
                      final url = details.url;
                      if (url.isNotEmpty) {
                        // Keep it noop here; web build will handle by Navigator or url_launcher if added later
                      }
                    },
                    child: const Text('View on Google'),
                  ),
                ],
              ),
            );

            // Grid layout for a more professional look
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  header,
                  const SizedBox(height: 8),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 12,
                      crossAxisSpacing: 12,
                      childAspectRatio: 2.8,
                    ),
                    itemCount: reviews.length.clamp(0, 6),
                    itemBuilder: (context, index) {
                      final r = reviews[index];
                      return Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.06),
                              blurRadius: 10,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 14,
                                  backgroundImage: r.profilePhotoUrl != null
                                      ? NetworkImage(r.profilePhotoUrl!)
                                      : null,
                                  child: r.profilePhotoUrl == null
                                      ? const Icon(Icons.person, size: 16)
                                      : null,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    r.authorName,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(fontWeight: FontWeight.w600),
                                  ),
                                ),
                                Row(
                                  children: List.generate(5, (i) {
                                    final filled = r.rating >= i + 1;
                                    final half = r.rating > i && r.rating < i + 1;
                                    return Icon(
                                      filled
                                          ? Icons.star
                                          : half
                                              ? Icons.star_half
                                              : Icons.star_border,
                                      color: Colors.amber[700],
                                      size: 14,
                                    );
                                  }),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Expanded(
                              child: Text(
                                r.text,
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(color: MyTheme.font_grey, height: 1.3),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}

